#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
若依Vue Pro包名冲突自动解决工具
用于处理上游同步时的包名和路径冲突问题
"""

import os
import re
import sys
import shutil
from pathlib import Path

class ConflictResolver:
    def __init__(self):
        # 包名映射配置
        self.mappings = {
            'cn.iocoder.yudao': 'com.yourcompany.platform',
            'cn.iocoder.boot': 'com.yourcompany.platform',
            'yudao': 'business-platform',
            'Yudao': 'BusinessPlatform',
            '芋道管理系统': '企业业务管理平台',
            '芋道项目': '企业平台项目'
        }
        
        # 需要处理的文件类型
        self.file_extensions = {'.java', '.xml', '.yml', '.yaml', '.properties', '.js', '.ts', '.vue', '.md'}
        
        # 忽略的目录
        self.ignore_dirs = {'.git', 'target', 'node_modules', '.idea', 'dist'}

    def resolve_conflicts(self, project_path):
        """解决项目中的包名冲突"""
        print(f"🔧 开始解决冲突：{project_path}")
        
        project_path = Path(project_path)
        if not project_path.exists():
            print(f"❌ 项目路径不存在：{project_path}")
            return False
            
        # 1. 处理文件内容
        self._process_file_contents(project_path)
        
        # 2. 处理文件路径
        self._process_file_paths(project_path)
        
        print("✅ 冲突解决完成！")
        return True

    def _process_file_contents(self, project_path):
        """处理文件内容中的包名替换"""
        print("📝 处理文件内容...")
        
        processed_count = 0
        for file_path in self._get_files_to_process(project_path):
            if self._process_single_file(file_path):
                processed_count += 1
                
        print(f"📊 已处理 {processed_count} 个文件")

    def _process_single_file(self, file_path):
        """处理单个文件"""
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 应用所有映射规则
            for old_name, new_name in self.mappings.items():
                content = content.replace(old_name, new_name)
            
            # 如果内容有变化，写回文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  ✓ 已更新：{file_path.name}")
                return True
                
        except Exception as e:
            print(f"  ❌ 处理失败：{file_path} - {e}")
            
        return False

    def _process_file_paths(self, project_path):
        """处理文件路径重命名"""
        print("📁 处理文件路径...")
        
        # 收集需要重命名的路径
        paths_to_rename = []
        
        for root, dirs, files in os.walk(project_path):
            # 跳过忽略的目录
            dirs[:] = [d for d in dirs if d not in self.ignore_dirs]
            
            root_path = Path(root)
            
            # 检查目录名
            for dir_name in dirs:
                if self._needs_rename(dir_name):
                    old_path = root_path / dir_name
                    new_name = self._apply_path_mappings(dir_name)
                    new_path = root_path / new_name
                    paths_to_rename.append((old_path, new_path))
            
            # 检查文件名
            for file_name in files:
                if self._needs_rename(file_name):
                    old_path = root_path / file_name
                    new_name = self._apply_path_mappings(file_name)
                    new_path = root_path / new_name
                    paths_to_rename.append((old_path, new_path))
        
        # 执行重命名（从深层到浅层，避免路径冲突）
        paths_to_rename.sort(key=lambda x: len(str(x[0])), reverse=True)
        
        for old_path, new_path in paths_to_rename:
            try:
                if old_path.exists() and not new_path.exists():
                    shutil.move(str(old_path), str(new_path))
                    print(f"  ✓ 重命名：{old_path.name} -> {new_path.name}")
            except Exception as e:
                print(f"  ❌ 重命名失败：{old_path} -> {new_path} - {e}")

    def _get_files_to_process(self, project_path):
        """获取需要处理的文件列表"""
        for root, dirs, files in os.walk(project_path):
            # 跳过忽略的目录
            dirs[:] = [d for d in dirs if d not in self.ignore_dirs]
            
            for file_name in files:
                file_path = Path(root) / file_name
                if file_path.suffix in self.file_extensions:
                    yield file_path

    def _needs_rename(self, name):
        """检查名称是否需要重命名"""
        return any(old_name in name for old_name in self.mappings.keys())

    def _apply_path_mappings(self, name):
        """应用路径映射规则"""
        result = name
        for old_name, new_name in self.mappings.items():
            result = result.replace(old_name, new_name)
        return result

def main():
    if len(sys.argv) != 2:
        print("用法：python conflict-resolver.py <项目路径>")
        print("示例：python conflict-resolver.py g:\\devAI\\ruoyi-vue-pro-1735-enterprise")
        sys.exit(1)
    
    project_path = sys.argv[1]
    resolver = ConflictResolver()
    
    print("=" * 50)
    print("若依Vue Pro包名冲突自动解决工具")
    print("=" * 50)
    
    success = resolver.resolve_conflicts(project_path)
    
    if success:
        print("\n🎉 所有冲突已解决！")
        print("\n下一步建议：")
        print("1. 检查代码变更：git diff")
        print("2. 运行编译测试：mvn clean compile")
        print("3. 提交变更：git add . && git commit -m 'fix: 解决包名冲突'")
    else:
        print("\n❌ 冲突解决失败，请检查错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
