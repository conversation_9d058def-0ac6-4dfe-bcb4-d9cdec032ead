@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

echo ========================================
echo 若依Vue Pro上游同步工具
echo ========================================
echo.

:: 配置参数 - 请根据实际情况修改
set "ORIGINAL_REPO_PATH=g:\devAI\ruoyi-original"
set "ENTERPRISE_REPO_PATH=g:\devAI\ruoyi-vue-pro-1735-enterprise"
set "UPSTREAM_URL=https://gitee.com/zhijiantianya/ruoyi-vue-pro.git"

:: 包名映射配置
set "OLD_PACKAGE=cn.iocoder.yudao"
set "NEW_PACKAGE=com.yourcompany.platform"
set "OLD_ARTIFACT=yudao"
set "NEW_ARTIFACT=business-platform"
set "OLD_TITLE=芋道管理系统"
set "NEW_TITLE=企业业务管理平台"

echo [1/6] 检查原始仓库...
if not exist "%ORIGINAL_REPO_PATH%" (
    echo 错误：原始仓库不存在，正在克隆...
    git clone %UPSTREAM_URL% "%ORIGINAL_REPO_PATH%"
    if errorlevel 1 (
        echo 克隆失败，请检查网络连接
        pause
        exit /b 1
    )
)

echo [2/6] 同步上游更新...
cd /d "%ORIGINAL_REPO_PATH%"
git fetch upstream
git checkout main
git merge upstream/main
if errorlevel 1 (
    echo 警告：合并时发现冲突，请手动解决
    pause
)

echo [3/6] 获取更新列表...
git log --oneline --since="1 month ago" > "%TEMP%\upstream_changes.txt"
echo 最近一个月的更新：
type "%TEMP%\upstream_changes.txt"
echo.

echo [4/6] 准备应用到企业版本...
set /p APPLY_UPDATES="是否要将这些更新应用到企业版本？(y/n): "
if /i not "%APPLY_UPDATES%"=="y" (
    echo 取消同步操作
    pause
    exit /b 0
)

echo [5/6] 创建补丁文件...
git format-patch --since="1 month ago" -o "%TEMP%\patches"

echo [6/6] 应用补丁到企业版本...
cd /d "%ENTERPRISE_REPO_PATH%"
git checkout -b sync-upstream-%date:~0,4%%date:~5,2%%date:~8,2%

:: 应用补丁并自动转换包名
for %%f in ("%TEMP%\patches\*.patch") do (
    echo 处理补丁: %%~nxf
    
    :: 先转换补丁文件中的包名
    powershell -Command "(Get-Content '%%f') -replace '%OLD_PACKAGE%', '%NEW_PACKAGE%' -replace '%OLD_ARTIFACT%', '%NEW_ARTIFACT%' -replace '%OLD_TITLE%', '%NEW_TITLE%' | Set-Content '%%f.converted'"
    
    :: 应用转换后的补丁
    git apply --ignore-whitespace "%%f.converted"
    if errorlevel 1 (
        echo 警告：补丁 %%~nxf 应用失败，可能需要手动处理
        echo 失败的补丁已保存到：%%f.converted
    )
)

echo.
echo ========================================
echo 同步完成！
echo ========================================
echo 新分支：sync-upstream-%date:~0,4%%date:~5,2%%date:~8,2%
echo 请检查更改并测试后合并到主分支
echo.
echo 下一步操作：
echo 1. 检查代码变更：git diff main
echo 2. 运行测试确保功能正常
echo 3. 合并到主分支：git checkout main ^&^& git merge sync-upstream-%date:~0,4%%date:~5,2%%date:~8,2%
echo.
pause
